@echo off
REM ========================================
REM   WitMall 环境变量配置
REM ========================================

REM 请根据您的Linux服务器实际地址修改以下配置

REM Nacos服务器地址
set NACOS_SERVER_ADDR=*************:8848

REM Redis服务器配置
set REDIS_HOST=*************
set REDIS_PORT=6379
set REDIS_PASSWORD=

REM MySQL服务器配置
set MYSQL_HOST=*************
set MYSQL_PORT=3306
set MYSQL_USERNAME=root
set MYSQL_PASSWORD=123456

REM JWT密钥配置
set JWT_SECRET=wit-mall-production-secret-key-2024-very-long-secret-for-security

REM 其他配置
set SPRING_PROFILES_ACTIVE=dev

echo ========================================
echo   环境变量配置完成
echo ========================================
echo Nacos服务器: %NACOS_SERVER_ADDR%
echo Redis服务器: %REDIS_HOST%:%REDIS_PORT%
echo MySQL服务器: %MYSQL_HOST%:%MYSQL_PORT%
echo 激活配置: %SPRING_PROFILES_ACTIVE%
echo ========================================
echo.
echo 请根据您的实际服务器地址修改此文件中的IP地址
echo 然后运行 start-gateway.bat 启动网关服务
echo.
