@echo off
echo ========================================
echo    一次性下载所有依赖
echo ========================================

echo.
echo 这个脚本会一次性下载所有项目依赖到本地仓库
echo 之后启动应用就不需要重新下载了
echo.

echo 1. 下载根项目依赖...
mvn dependency:resolve dependency:resolve-sources -q
if %errorlevel% neq 0 (
    echo ❌ 根项目依赖下载失败
    pause
    exit /b 1
)
echo ✅ 根项目依赖下载完成

echo.
echo 2. 下载 wit-common 依赖...
cd wit-common
mvn dependency:resolve dependency:resolve-sources -q
if %errorlevel% neq 0 (
    echo ❌ wit-common 依赖下载失败
    cd ..
    pause
    exit /b 1
)
echo ✅ wit-common 依赖下载完成
cd ..

echo.
echo 3. 下载 wit-gateway 依赖...
cd wit-gateway
mvn dependency:resolve dependency:resolve-sources -q
if %errorlevel% neq 0 (
    echo ❌ wit-gateway 依赖下载失败
    cd ..
    pause
    exit /b 1
)
echo ✅ wit-gateway 依赖下载完成

echo.
echo 4. 下载 Spring Boot 插件依赖...
mvn spring-boot:help -q
if %errorlevel% neq 0 (
    echo ❌ Spring Boot 插件依赖下载失败
    cd ..
    pause
    exit /b 1
)
echo ✅ Spring Boot 插件依赖下载完成

echo.
echo 5. 复制依赖到 lib 目录（可选，用于直接 Java 启动）...
mvn dependency:copy-dependencies -DoutputDirectory=target/lib -q
if %errorlevel% neq 0 (
    echo ⚠️ 依赖复制失败，但不影响 Maven 启动
) else (
    echo ✅ 依赖复制到 target/lib 完成
)

cd ..

echo.
echo 6. 下载其他微服务依赖...
for %%d in (wit-user wit-product wit-order) do (
    if exist "%%d" (
        echo 下载 %%d 依赖...
        cd %%d
        mvn dependency:resolve -q
        cd ..
        echo ✅ %%d 依赖下载完成
    )
)

echo.
echo ========================================
echo 所有依赖下载完成！
echo ========================================
echo.
echo 现在你可以使用以下方式快速启动：
echo.
echo 1. 使用优化启动脚本：
echo    start-gateway-optimized.bat
echo.
echo 2. 使用离线模式：
echo    cd wit-gateway
echo    mvn spring-boot:run -o -DskipTests
echo.
echo 3. 直接使用 Java（如果复制了依赖）：
echo    cd wit-gateway
echo    java -cp "target/classes;target/lib/*" com.wit.gateway.WitGatewayApplication
echo.
pause
