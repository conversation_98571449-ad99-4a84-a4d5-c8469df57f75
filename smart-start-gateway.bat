@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    智能网关启动脚本
echo ========================================

REM 设置环境变量
set NACOS_SERVER_ADDR=***************:8848
set REDIS_HOST=***************
set REDIS_PORT=6379
set REDIS_PASSWORD=
set MYSQL_HOST=***************
set MYSQL_PORT=3306
set MYSQL_DATABASE=wit_mall
set MYSQL_USERNAME=root
set MYSQL_PASSWORD=root123456
set RABBITMQ_HOST=***************
set RABBITMQ_PORT=5672
set RABBITMQ_USERNAME=admin
set RABBITMQ_PASSWORD=admin123

echo.
echo 1. 检查环境...
echo ✅ 环境变量设置完成

REM 检查是否已经编译过
echo.
echo 2. 检查编译状态...
if exist "wit-gateway\target\classes\com\wit\gateway\WitGatewayApplication.class" (
    echo ✅ 网关已编译
    set COMPILED=true
) else (
    echo ⚠️ 网关未编译
    set COMPILED=false
)

REM 检查依赖是否已下载
echo.
echo 3. 检查依赖状态...
set DEPS_EXIST=true

REM 检查关键依赖文件
if not exist "%USERPROFILE%\.m2\repository\org\springframework\boot\spring-boot-starter-webflux" (
    set DEPS_EXIST=false
)
if not exist "%USERPROFILE%\.m2\repository\org\springframework\cloud\spring-cloud-starter-gateway" (
    set DEPS_EXIST=false
)
if not exist "%USERPROFILE%\.m2\repository\com\alibaba\cloud\spring-cloud-starter-alibaba-nacos-discovery" (
    set DEPS_EXIST=false
)

if "!DEPS_EXIST!"=="true" (
    echo ✅ 主要依赖已存在
    set USE_OFFLINE=true
) else (
    echo ⚠️ 部分依赖缺失，需要在线下载
    set USE_OFFLINE=false
)

echo.
echo 4. 选择启动模式...

if "!COMPILED!"=="true" if "!USE_OFFLINE!"=="true" (
    echo 🚀 使用快速离线模式启动
    goto :offline_start
) else (
    echo 🌐 使用在线模式启动（首次启动或依赖缺失）
    goto :online_start
)

:offline_start
echo.
echo ========================================
echo    离线快速启动
echo ========================================
cd wit-gateway
echo 正在启动网关服务（离线模式）...
mvn spring-boot:run -o -DskipTests ^
  -Dspring-boot.run.jvmArguments="-DNACOS_SERVER_ADDR=%NACOS_SERVER_ADDR% -DREDIS_HOST=%REDIS_HOST% -DREDIS_PORT=%REDIS_PORT% -DREDIS_PASSWORD=%REDIS_PASSWORD% -DMYSQL_HOST=%MYSQL_HOST% -DMYSQL_PORT=%MYSQL_PORT% -DMYSQL_DATABASE=%MYSQL_DATABASE% -DMYSQL_USERNAME=%MYSQL_USERNAME% -DMYSQL_PASSWORD=%MYSQL_PASSWORD% -DRABBITMQ_HOST=%RABBITMQ_HOST% -DRABBITMQ_PORT=%RABBITMQ_PORT% -DRABBITMQ_USERNAME=%RABBITMQ_USERNAME% -DRABBITMQ_PASSWORD=%RABBITMQ_PASSWORD%"
goto :end

:online_start
echo.
echo ========================================
echo    在线启动（下载依赖）
echo ========================================

REM 首先编译 common 模块
echo 编译 common 模块...
mvn clean compile -DskipTests -pl wit-common -am -q
if %errorlevel% neq 0 (
    echo ❌ common 模块编译失败
    goto :error
)

cd wit-gateway
echo 正在启动网关服务（在线模式）...
mvn spring-boot:run -DskipTests ^
  -Dspring-boot.run.jvmArguments="-DNACOS_SERVER_ADDR=%NACOS_SERVER_ADDR% -DREDIS_HOST=%REDIS_HOST% -DREDIS_PORT=%REDIS_PORT% -DREDIS_PASSWORD=%REDIS_PASSWORD% -DMYSQL_HOST=%MYSQL_HOST% -DMYSQL_PORT=%MYSQL_PORT% -DMYSQL_DATABASE=%MYSQL_DATABASE% -DMYSQL_USERNAME=%MYSQL_USERNAME% -DMYSQL_PASSWORD=%MYSQL_PASSWORD% -DRABBITMQ_HOST=%RABBITMQ_HOST% -DRABBITMQ_PORT=%RABBITMQ_PORT% -DRABBITMQ_USERNAME=%RABBITMQ_USERNAME% -DRABBITMQ_PASSWORD=%RABBITMQ_PASSWORD%"
goto :end

:error
echo.
echo ❌ 启动失败
cd ..
pause
exit /b 1

:end
cd ..
echo.
echo 网关服务已停止
pause
