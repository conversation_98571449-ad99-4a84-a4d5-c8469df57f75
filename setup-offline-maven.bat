@echo off
echo ========================================
echo    Maven 离线依赖设置脚本
echo ========================================

echo.
echo 这个脚本将：
echo 1. 一次性下载所有项目依赖
echo 2. 创建离线依赖包
echo 3. 配置 Maven 使用本地依赖
echo 4. 避免每次启动都重新下载
echo.

set MAVEN_OPTS=-Xmx2g -XX:MaxPermSize=512m

echo 1. 清理并下载所有依赖...
echo.

REM 下载根项目依赖
echo [1/6] 下载根项目依赖...
mvn dependency:resolve dependency:resolve-sources dependency:resolve-javadoc -q
if %errorlevel% neq 0 (
    echo ❌ 根项目依赖下载失败
    pause
    exit /b 1
)

REM 下载 common 模块依赖
echo [2/6] 下载 wit-common 依赖...
cd wit-common
mvn dependency:resolve dependency:resolve-sources -q
mvn dependency:copy-dependencies -DoutputDirectory=target/lib -q
cd ..

REM 下载 gateway 模块依赖
echo [3/6] 下载 wit-gateway 依赖...
cd wit-gateway
mvn dependency:resolve dependency:resolve-sources -q
mvn dependency:copy-dependencies -DoutputDirectory=target/lib -q
cd ..

REM 下载其他模块依赖
for %%d in (wit-user wit-product wit-order) do (
    if exist "%%d" (
        echo [4/6] 下载 %%d 依赖...
        cd %%d
        mvn dependency:resolve dependency:resolve-sources -q
        mvn dependency:copy-dependencies -DoutputDirectory=target/lib -q
        cd ..
    )
)

echo [5/6] 下载 Spring Boot 插件依赖...
cd wit-gateway
mvn spring-boot:help -q
cd ..

echo [6/6] 创建离线启动脚本...

REM 创建网关离线启动脚本
echo @echo off > start-gateway-offline.bat
echo echo ======================================== >> start-gateway-offline.bat
echo echo    网关服务离线启动 >> start-gateway-offline.bat
echo echo ======================================== >> start-gateway-offline.bat
echo echo. >> start-gateway-offline.bat
echo echo 设置环境变量... >> start-gateway-offline.bat
echo set NACOS_SERVER_ADDR=***************:8848 >> start-gateway-offline.bat
echo set REDIS_HOST=*************** >> start-gateway-offline.bat
echo set REDIS_PORT=6379 >> start-gateway-offline.bat
echo set REDIS_PASSWORD= >> start-gateway-offline.bat
echo set MYSQL_HOST=*************** >> start-gateway-offline.bat
echo set MYSQL_PORT=3306 >> start-gateway-offline.bat
echo set MYSQL_DATABASE=wit_mall >> start-gateway-offline.bat
echo set MYSQL_USERNAME=root >> start-gateway-offline.bat
echo set MYSQL_PASSWORD=root123456 >> start-gateway-offline.bat
echo set RABBITMQ_HOST=*************** >> start-gateway-offline.bat
echo set RABBITMQ_PORT=5672 >> start-gateway-offline.bat
echo set RABBITMQ_USERNAME=admin >> start-gateway-offline.bat
echo set RABBITMQ_PASSWORD=admin123 >> start-gateway-offline.bat
echo echo ✅ 环境变量设置完成 >> start-gateway-offline.bat
echo echo. >> start-gateway-offline.bat
echo echo 启动网关服务（离线模式）... >> start-gateway-offline.bat
echo cd wit-gateway >> start-gateway-offline.bat
echo mvn spring-boot:run -o -DskipTests ^^ >> start-gateway-offline.bat
echo   -Dspring-boot.run.jvmArguments="-DNACOS_SERVER_ADDR=%%NACOS_SERVER_ADDR%% -DREDIS_HOST=%%REDIS_HOST%% -DREDIS_PORT=%%REDIS_PORT%% -DREDIS_PASSWORD=%%REDIS_PASSWORD%% -DMYSQL_HOST=%%MYSQL_HOST%% -DMYSQL_PORT=%%MYSQL_PORT%% -DMYSQL_DATABASE=%%MYSQL_DATABASE%% -DMYSQL_USERNAME=%%MYSQL_USERNAME%% -DMYSQL_PASSWORD=%%MYSQL_PASSWORD%% -DRABBITMQ_HOST=%%RABBITMQ_HOST%% -DRABBITMQ_PORT=%%RABBITMQ_PORT%% -DRABBITMQ_USERNAME=%%RABBITMQ_USERNAME%% -DRABBITMQ_PASSWORD=%%RABBITMQ_PASSWORD%%" >> start-gateway-offline.bat
echo cd .. >> start-gateway-offline.bat
echo pause >> start-gateway-offline.bat

echo.
echo ========================================
echo 离线依赖设置完成！
echo ========================================
echo.
echo 现在你可以使用以下方式启动：
echo.
echo 1. 离线启动（推荐）：
echo    start-gateway-offline.bat
echo.
echo 2. 手动离线启动：
echo    cd wit-gateway
echo    mvn spring-boot:run -o -DskipTests
echo.
echo 3. 直接 Java 启动：
echo    cd wit-gateway
echo    java -cp "target/classes;target/lib/*" com.wit.gateway.WitGatewayApplication
echo.
echo 注意：-o 参数表示离线模式，不会重新下载依赖
echo.
pause
