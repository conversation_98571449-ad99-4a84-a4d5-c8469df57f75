@echo off
echo ========================================
echo    测试网关服务启动
echo ========================================

echo.
echo 1. 检查Java环境...
java -version
if %errorlevel% neq 0 (
    echo ❌ Java环境未配置，请安装JDK 17+
    pause
    exit /b 1
)

echo.
echo 2. 编译网关服务...
cd wit-gateway
mvn clean compile -DskipTests -q
if %errorlevel% neq 0 (
    echo ❌ 网关服务编译失败
    cd ..
    pause
    exit /b 1
)
echo ✅ 网关服务编译成功

echo.
echo 3. 检查依赖...
mvn dependency:tree -DoutputFile=dependency-tree.txt -q
if exist dependency-tree.txt (
    echo ✅ 依赖检查完成，详情请查看 wit-gateway/dependency-tree.txt
) else (
    echo ⚠️ 依赖检查可能有问题
)

cd ..

echo.
echo ========================================
echo 测试完成！
echo ========================================
echo.
echo 如果编译成功，说明配置修复已生效
echo 现在可以尝试启动网关服务：
echo   cd wit-gateway
echo   mvn spring-boot:run
echo.
pause
