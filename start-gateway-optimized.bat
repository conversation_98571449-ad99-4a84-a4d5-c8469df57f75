@echo off
echo ========================================
echo    网关服务优化启动脚本
echo ========================================

echo.
echo 1. 设置环境变量...
set NACOS_SERVER_ADDR=***************:8848
set REDIS_HOST=***************
set REDIS_PORT=6379
set REDIS_PASSWORD=
set MYSQL_HOST=***************
set MYSQL_PORT=3306
set MYSQL_DATABASE=wit_mall
set MYSQL_USERNAME=root
set MYSQL_PASSWORD=root123456
set RABBITMQ_HOST=***************
set RABBITMQ_PORT=5672
set RABBITMQ_USERNAME=admin
set RABBITMQ_PASSWORD=admin123

echo ✅ 环境变量设置完成

echo.
echo 2. 检查是否已编译...
cd wit-gateway
if exist "target\classes\com\wit\gateway\WitGatewayApplication.class" (
    echo ✅ 应用已编译，跳过编译步骤
    goto :start_app
) else (
    echo ⚠️ 应用未编译，开始编译...
    goto :compile_app
)

:compile_app
echo.
echo 3. 编译应用（仅编译，不下载依赖）...
mvn compile -DskipTests -o 2>nul
if %errorlevel% neq 0 (
    echo ❌ 离线编译失败，尝试在线编译...
    mvn clean compile -DskipTests -q
    if %errorlevel% neq 0 (
        echo ❌ 编译失败
        cd ..
        pause
        exit /b 1
    )
)
echo ✅ 编译成功

:start_app
echo.
echo 4. 启动应用（使用已编译的类）...
echo 配置信息：
echo   - 服务端口: 8080
echo   - Nacos地址: %NACOS_SERVER_ADDR%
echo   - Redis地址: %REDIS_HOST%:%REDIS_PORT%
echo   - MySQL地址: %MYSQL_HOST%:%MYSQL_PORT%
echo.

REM 直接使用 java 命令启动，避免 Maven 重新检查依赖
echo 正在启动网关服务...

REM 构建 classpath
set CLASSPATH=target\classes
for %%i in (target\lib\*.jar) do set CLASSPATH=!CLASSPATH!;%%i

REM 如果没有 lib 目录，使用 Maven 依赖
if not exist "target\lib" (
    echo 使用 Maven 依赖启动...
    mvn spring-boot:run -DskipTests ^
        -Dspring-boot.run.jvmArguments="-DNACOS_SERVER_ADDR=%NACOS_SERVER_ADDR% -DREDIS_HOST=%REDIS_HOST% -DREDIS_PORT=%REDIS_PORT% -DREDIS_PASSWORD=%REDIS_PASSWORD% -DMYSQL_HOST=%MYSQL_HOST% -DMYSQL_PORT=%MYSQL_PORT% -DMYSQL_DATABASE=%MYSQL_DATABASE% -DMYSQL_USERNAME=%MYSQL_USERNAME% -DMYSQL_PASSWORD=%MYSQL_PASSWORD% -DRABBITMQ_HOST=%RABBITMQ_HOST% -DRABBITMQ_PORT=%RABBITMQ_PORT% -DRABBITMQ_USERNAME=%RABBITMQ_USERNAME% -DRABBITMQ_PASSWORD=%RABBITMQ_PASSWORD%"
) else (
    echo 使用直接 Java 启动...
    java -cp "%CLASSPATH%" ^
        -DNACOS_SERVER_ADDR=%NACOS_SERVER_ADDR% ^
        -DREDIS_HOST=%REDIS_HOST% ^
        -DREDIS_PORT=%REDIS_PORT% ^
        -DREDIS_PASSWORD=%REDIS_PASSWORD% ^
        -DMYSQL_HOST=%MYSQL_HOST% ^
        -DMYSQL_PORT=%MYSQL_PORT% ^
        -DMYSQL_DATABASE=%MYSQL_DATABASE% ^
        -DMYSQL_USERNAME=%MYSQL_USERNAME% ^
        -DMYSQL_PASSWORD=%MYSQL_PASSWORD% ^
        -DRABBITMQ_HOST=%RABBITMQ_HOST% ^
        -DRABBITMQ_PORT=%RABBITMQ_PORT% ^
        -DRABBITMQ_USERNAME=%RABBITMQ_USERNAME% ^
        -DRABBITMQ_PASSWORD=%RABBITMQ_PASSWORD% ^
        com.wit.gateway.WitGatewayApplication
)

cd ..
echo.
echo 网关服务已停止
pause
