<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 
          http://maven.apache.org/xsd/settings-1.0.0.xsd">

  <!-- 本地仓库路径 -->
  <localRepository>${user.home}/.m2/repository</localRepository>

  <!-- 离线模式 -->
  <offline>false</offline>

  <!-- 插件组 -->
  <pluginGroups>
    <pluginGroup>org.springframework.boot</pluginGroup>
    <pluginGroup>org.apache.maven.plugins</pluginGroup>
  </pluginGroups>

  <!-- 代理配置（如果需要） -->
  <proxies>
    <!-- 
    <proxy>
      <id>example-proxy</id>
      <active>true</active>
      <protocol>http</protocol>
      <host>proxy.example.com</host>
      <port>8080</port>
    </proxy>
    -->
  </proxies>

  <!-- 服务器配置 -->
  <servers>
    <!-- 
    <server>
      <id>deploymentRepo</id>
      <username>repouser</username>
      <password>repopwd</password>
    </server>
    -->
  </servers>

  <!-- 镜像配置 -->
  <mirrors>
    <!-- 阿里云镜像 -->
    <mirror>
      <id>aliyunmaven</id>
      <mirrorOf>*</mirrorOf>
      <name>阿里云公共仓库</name>
      <url>https://maven.aliyun.com/repository/public</url>
    </mirror>
  </mirrors>

  <!-- 配置文件 -->
  <profiles>
    <!-- 离线开发配置 -->
    <profile>
      <id>offline-dev</id>
      <properties>
        <!-- 跳过测试 -->
        <maven.test.skip>true</maven.test.skip>
        <!-- 跳过 Javadoc -->
        <maven.javadoc.skip>true</maven.javadoc.skip>
        <!-- 跳过源码打包 -->
        <maven.source.skip>true</maven.source.skip>
        <!-- 强制使用本地仓库 -->
        <maven.repo.local>${settings.localRepository}</maven.repo.local>
        <!-- 禁用快照更新 -->
        <maven.snapshot.update>false</maven.snapshot.update>
        <!-- 禁用发布版本更新检查 -->
        <maven.version.update>false</maven.version.update>
      </properties>
      
      <repositories>
        <repository>
          <id>central</id>
          <name>Central Repository</name>
          <url>https://maven.aliyun.com/repository/central</url>
          <layout>default</layout>
          <snapshots>
            <enabled>false</enabled>
            <updatePolicy>never</updatePolicy>
          </snapshots>
          <releases>
            <enabled>true</enabled>
            <updatePolicy>never</updatePolicy>
          </releases>
        </repository>
        
        <repository>
          <id>spring-releases</id>
          <name>Spring Releases</name>
          <url>https://maven.aliyun.com/repository/spring</url>
          <snapshots>
            <enabled>false</enabled>
            <updatePolicy>never</updatePolicy>
          </snapshots>
          <releases>
            <enabled>true</enabled>
            <updatePolicy>never</updatePolicy>
          </releases>
        </repository>
      </repositories>
      
      <pluginRepositories>
        <pluginRepository>
          <id>central</id>
          <name>Central Repository</name>
          <url>https://maven.aliyun.com/repository/central</url>
          <layout>default</layout>
          <snapshots>
            <enabled>false</enabled>
            <updatePolicy>never</updatePolicy>
          </snapshots>
          <releases>
            <enabled>true</enabled>
            <updatePolicy>never</updatePolicy>
          </releases>
        </pluginRepository>
        
        <pluginRepository>
          <id>spring-releases</id>
          <name>Spring Releases</name>
          <url>https://maven.aliyun.com/repository/spring</url>
          <snapshots>
            <enabled>false</enabled>
            <updatePolicy>never</updatePolicy>
          </snapshots>
          <releases>
            <enabled>true</enabled>
            <updatePolicy>never</updatePolicy>
          </releases>
        </pluginRepository>
      </pluginRepositories>
    </profile>

    <!-- 在线开发配置 -->
    <profile>
      <id>online-dev</id>
      <properties>
        <maven.test.skip>true</maven.test.skip>
        <maven.javadoc.skip>true</maven.javadoc.skip>
      </properties>
      
      <repositories>
        <repository>
          <id>central</id>
          <name>Central Repository</name>
          <url>https://maven.aliyun.com/repository/central</url>
          <layout>default</layout>
          <snapshots>
            <enabled>false</enabled>
          </snapshots>
        </repository>
      </repositories>
      
      <pluginRepositories>
        <pluginRepository>
          <id>central</id>
          <name>Central Repository</name>
          <url>https://maven.aliyun.com/repository/central</url>
          <layout>default</layout>
          <snapshots>
            <enabled>false</enabled>
          </snapshots>
        </pluginRepository>
      </pluginRepositories>
    </profile>
  </profiles>

  <!-- 激活的配置文件 -->
  <activeProfiles>
    <activeProfile>offline-dev</activeProfile>
  </activeProfiles>

</settings>
